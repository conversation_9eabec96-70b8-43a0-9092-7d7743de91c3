{"name": "figma-agent-root", "version": "1.0.0", "description": "Figma AI Chat Plugin - Monorepo", "private": true, "workspaces": ["packages/*"], "scripts": {"build": "npm run build --workspace=packages/plugin && npm run build --workspace=packages/backend && npm run build --workspace=packages/mcp-server", "dev:backend": "npm run dev --workspace=packages/backend", "dev:plugin": "npm run watch --workspace=packages/plugin", "dev:mcp": "npm run dev --workspace=packages/mcp-server", "start:mcp-socket": "npm run socket --workspace=packages/mcp-server"}, "author": "", "license": "MIT"}